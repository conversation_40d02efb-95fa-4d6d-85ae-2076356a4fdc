"""
Chat routes for TradingIA Backend.

This module defines the main chat endpoint that orchestrates
authentication, AI interaction, and tool execution.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.models.chat import Chat<PERSON><PERSON><PERSON>, ChatResponse
from app.services.supabase_client import validate_user_token, save_chat_history
from app.services.vertex_ai import initialize_gemini_model, generate_chat_response, process_function_call_result
from app.tools.tradingview_provider import get_available_tools, get_price_data, apply_indicator
from app.services.market_data_service import market_data_service
from typing import Dict, Any
import logging
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/chat", tags=["chat"])

# Security scheme
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Dependency to validate user authentication.
    
    This function extracts and validates the JWT token from the
    Authorization header and returns user information.
    
    Args:
        credentials: HTTP Bearer credentials from the request header
        
    Returns:
        Dict[str, Any]: User information if token is valid
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Validate the token using Supabase
        user_info = await validate_user_token(credentials.credentials)
        return user_info
        
    except HTTPException:
        # Re-raise authentication errors
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


@router.post("/debug", response_model=ChatResponse)
async def chat_debug_endpoint(request: ChatRequest) -> ChatResponse:
    """
    DEBUG endpoint for testing without authentication.
    ONLY FOR DEVELOPMENT - REMOVE IN PRODUCTION
    """
    print("🔧 === DEBUG ENDPOINT CALLED ===")
    print(f"🔧 Message: {request.message}")
    print(f"🔧 History: {len(request.history)} messages")

    # Create a fake user for testing
    fake_user = {
        "id": "debug-user-12345",
        "email": "<EMAIL>"
    }

    return await _process_chat_request(request, fake_user)

@router.post("/", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> ChatResponse:
    """
    Main chat endpoint for AI interactions.

    This endpoint handles user chat requests, validates authentication,
    processes the request through the AI model, executes any required
    tools, and returns the final response.

    Args:
        request: Chat request containing message and history
        current_user: Authenticated user information

    Returns:
        ChatResponse: AI-generated response

    Raises:
        HTTPException: If processing fails
    """
    return await _process_chat_request(request, current_user)

async def _process_chat_request(request: ChatRequest, current_user: Dict[str, Any]) -> ChatResponse:
    """
    Process a chat request with the given user context.
    NUEVA VERSIÓN: Sin herramientas para evitar thought_signature

    Args:
        request: Chat request containing message and history
        current_user: User information

    Returns:
        ChatResponse: AI-generated response
    """
    try:
        logger.info(f"Chat request from user: {current_user.get('email', 'unknown')}")

        # Initialize Gemini model WITHOUT tools using market data from database
        from app.services.vertex_ai import initialize_gemini_model_no_tools
        model = await initialize_gemini_model_no_tools()

        # Prepare conversation history
        conversation_messages = []

        # Add history messages
        for msg in request.history:
            conversation_messages.append({
                "role": msg.role,
                "content": msg.content
            })

        # Add current message
        conversation_messages.append({
            "role": "user",
            "content": request.message
        })

        # Generate response from AI (no tools, direct response)
        ai_response = await generate_chat_response(conversation_messages, model)

        # Get final response content (simplified since no tools)
        if ai_response.get("type") == "text":
            final_reply = ai_response["content"]
        else:
            final_reply = "Lo siento, no pude procesar tu solicitud completamente. Esta información es solo para fines educativos y no constituye asesoramiento financiero."

        # Create response object
        conversation_id = f"conv_{current_user['id'][:8]}"
        response = ChatResponse(
            reply=final_reply,
            timestamp=datetime.now(),
            conversation_id=conversation_id
        )

        # Save chat history to database
        try:
            # Prepare request messages with proper datetime serialization
            request_messages_to_save = []
            for msg in request.history:
                msg_dict = msg.model_dump()  # Usar model_dump en lugar de dict()
                # Convert datetime to ISO string if present
                if msg_dict.get('timestamp') and hasattr(msg_dict['timestamp'], 'isoformat'):
                    msg_dict['timestamp'] = msg_dict['timestamp'].isoformat()
                request_messages_to_save.append(msg_dict)

            # Add current user message with timestamp
            user_message_to_save = {
                "role": "user",
                "content": request.message,
                "timestamp": datetime.now().isoformat()
            }
            request_messages_to_save.append(user_message_to_save)

            await save_chat_history(
                user_id=current_user["id"],
                request_messages=request_messages_to_save,
                ai_response=final_reply,
                conversation_id=conversation_id
            )
            logger.info(f"Chat history saved for user: {current_user.get('email', 'unknown')}")
        except Exception as e:
            # Log error but don't fail the request
            logger.error(f"Failed to save chat history: {str(e)}")

        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


@router.get("/health")
async def chat_health():
    """Health check endpoint for the chat service."""
    return {
        "status": "healthy",
        "service": "chat",
        "timestamp": datetime.now().isoformat()
    }


@router.get("/market-analysis")
async def get_market_analysis(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Genera análisis general de mercado con tendencias y alertas.
    """
    try:
        logger.info(f"Market analysis request from user: {current_user.get('email', 'unknown')}")

        analysis = await market_data_service.generate_market_analysis()

        return {
            "analysis": analysis,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating market analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate market analysis"
        )


@router.get("/buy-opportunities")
async def get_buy_opportunities(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Analiza todos los activos y genera recomendaciones de compra
    basadas en múltiples indicadores técnicos.
    """
    try:
        logger.info(f"Buy opportunities request from user: {current_user.get('email', 'unknown')}")

        opportunities = await market_data_service.generate_buy_opportunities()

        return {
            "opportunities": opportunities,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating buy opportunities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate buy opportunities"
        )


# Debug endpoints (sin autenticación para testing)
@router.api_route("/market-analysis/debug", methods=["GET", "OPTIONS"])
async def get_market_analysis_debug():
    """
    Genera análisis general de mercado (DEBUG - sin autenticación).
    """
    try:
        analysis = await market_data_service.generate_market_analysis()

        return {
            "analysis": analysis,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating market analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate market analysis"
        )


@router.api_route("/buy-opportunities/debug", methods=["GET", "OPTIONS"])
async def get_buy_opportunities_debug():
    """
    Genera oportunidades de compra (DEBUG - sin autenticación).
    """
    try:
        opportunities = await market_data_service.generate_buy_opportunities()

        return {
            "opportunities": opportunities,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating buy opportunities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate buy opportunities"
        )


@router.post("/update-market-data/debug")
async def update_market_data_debug(
    batch_size: int = 10,
    specific_symbols: str = None
):
    """
    Actualiza manualmente los datos de mercado (DEBUG - sin autenticación).

    Args:
        batch_size: Tamaño del lote para procesamiento (default: 10)
        specific_symbols: Símbolos específicos separados por coma (opcional)
    """
    try:
        logger.info("🔄 Iniciando actualización manual de datos de mercado...")

        # Procesar símbolos específicos si se proporcionan
        if specific_symbols:
            symbols_list = [s.strip() for s in specific_symbols.split(",")]
            logger.info(f"📊 Actualizando símbolos específicos: {symbols_list}")
            result = await market_data_service.update_specific_assets(symbols_list, batch_size=batch_size)
        else:
            logger.info(f"📊 Actualizando todos los activos con batch_size: {batch_size}")
            result = await market_data_service.update_market_data(batch_size=batch_size)

        return {
            "message": "Actualización completada",
            "result": result,
            "parameters": {
                "batch_size": batch_size,
                "specific_symbols": specific_symbols,
                "symbols_processed": len(symbols_list) if specific_symbols else "all"
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error updating market data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update market data"
        )


@router.post("/scheduler/start")
async def start_scheduler(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Inicia el scheduler automático de datos de mercado.
    """
    try:
        from app.services.scheduler_service import scheduler_service

        logger.info(f"Start scheduler request from user: {current_user.get('email', 'unknown')}")

        result = scheduler_service.start()

        return {
            "message": "Scheduler start command executed",
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error starting scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start scheduler"
        )


@router.post("/scheduler/stop")
async def stop_scheduler(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Detiene el scheduler automático de datos de mercado.
    """
    try:
        from app.services.scheduler_service import scheduler_service

        logger.info(f"Stop scheduler request from user: {current_user.get('email', 'unknown')}")

        result = scheduler_service.stop()

        return {
            "message": "Scheduler stop command executed",
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error stopping scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop scheduler"
        )


@router.get("/scheduler/status")
async def get_scheduler_status(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Obtiene el estado actual del scheduler.
    """
    try:
        from app.services.scheduler_service import scheduler_service

        status_info = scheduler_service.get_status()

        return {
            "scheduler_status": status_info,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting scheduler status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get scheduler status"
        )
