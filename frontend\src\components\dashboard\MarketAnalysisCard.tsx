import React, { useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { useMarketAnalysis } from '@/hooks/useMarketAnalysis'
import { SchedulerControlPanel } from '@/components/dashboard/SchedulerControlPanel'
import { cn } from '@/utils/cn'
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  RefreshCw,
  Target,
  AlertTriangle,
  Clock,
  Settings
} from 'lucide-react'

interface MarketAnalysisCardProps {
  className?: string
  showSchedulerControls?: boolean
}

export const MarketAnalysisCard: React.FC<MarketAnalysisCardProps> = ({
  className,
  showSchedulerControls = true
}) => {
  const {
    marketAnalysis,
    isLoadingAnalysis,
    analysisError,
    fetchMarketAnalysis,
    buyOpportunities,
    isLoadingOpportunities,
    opportunitiesError,
    fetchBuyOpportunities,
    refreshAll
  } = useMarketAnalysis()

  // Auto-fetch on mount
  useEffect(() => {
    fetchMarketAnalysis()
  }, [fetchMarketAnalysis])

  const formatAnalysisText = (text: string) => {
    // Convert markdown-like formatting to JSX
    return text.split('\n').map((line, index) => {
      if (line.startsWith('📊') || line.startsWith('🎯')) {
        return (
          <div key={index} className="font-semibold text-primary mb-2">
            {line}
          </div>
        )
      }
      if (line.startsWith('🚀') || line.startsWith('📉')) {
        return (
          <div key={index} className="font-medium text-green-600 dark:text-green-400 mb-1">
            {line}
          </div>
        )
      }
      if (line.startsWith('⚠️')) {
        return (
          <div key={index} className="font-medium text-amber-600 dark:text-amber-400 mb-1">
            {line}
          </div>
        )
      }
      if (line.startsWith('•')) {
        return (
          <div key={index} className="text-sm text-muted-foreground ml-4 mb-1">
            {line}
          </div>
        )
      }
      if (line.trim() === '') {
        return <div key={index} className="mb-2" />
      }
      return (
        <div key={index} className="text-sm mb-1">
          {line}
        </div>
      )
    })
  }

  const formatOpportunitiesText = (text: string) => {
    return text.split('\n').map((line, index) => {
      if (line.startsWith('🎯') || line.startsWith('🥇') || line.startsWith('🥈')) {
        return (
          <div key={index} className="font-semibold text-primary mb-2">
            {line}
          </div>
        )
      }
      if (line.startsWith('•')) {
        return (
          <div key={index} className="text-sm bg-muted/50 p-2 rounded mb-2 ml-2">
            {line}
          </div>
        )
      }
      if (line.startsWith('  ')) {
        return (
          <div key={index} className="text-xs text-muted-foreground ml-6 mb-1">
            {line.trim()}
          </div>
        )
      }
      if (line.startsWith('⚠️')) {
        return (
          <div key={index} className="font-medium text-amber-600 dark:text-amber-400 mb-1">
            {line}
          </div>
        )
      }
      if (line.trim() === '') {
        return <div key={index} className="mb-1" />
      }
      return (
        <div key={index} className="text-sm mb-1">
          {line}
        </div>
      )
    })
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Market Analysis Card */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Análisis de Mercado</CardTitle>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchMarketAnalysis}
              disabled={isLoadingAnalysis}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn(
                "h-4 w-4",
                isLoadingAnalysis && "animate-spin"
              )} />
            </Button>
          </div>
          <CardDescription>
            Tendencias y alertas del mercado en tiempo real
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingAnalysis ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span className="text-sm">Analizando mercado...</span>
              </div>
            </div>
          ) : analysisError ? (
            <div className="flex items-center gap-2 text-destructive py-4">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">{analysisError}</span>
            </div>
          ) : marketAnalysis ? (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {formatAnalysisText(marketAnalysis.analysis)}
              <div className="flex items-center gap-1 text-xs text-muted-foreground pt-2 border-t">
                <Clock className="h-3 w-3" />
                <span>
                  Actualizado: {new Date(marketAnalysis.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Haz clic en actualizar para ver el análisis</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Buy Opportunities Card */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-600" />
              <CardTitle className="text-lg">Oportunidades de Compra</CardTitle>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchBuyOpportunities}
              disabled={isLoadingOpportunities}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn(
                "h-4 w-4",
                isLoadingOpportunities && "animate-spin"
              )} />
            </Button>
          </div>
          <CardDescription>
            Activos recomendados basados en análisis técnico
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!buyOpportunities ? (
            <Button
              onClick={fetchBuyOpportunities}
              disabled={isLoadingOpportunities}
              className="w-full"
              variant="default"
            >
              {isLoadingOpportunities ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Analizando oportunidades...
                </>
              ) : (
                <>
                  <Target className="h-4 w-4 mr-2" />
                  Ver Oportunidades de Compra
                </>
              )}
            </Button>
          ) : isLoadingOpportunities ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span className="text-sm">Analizando oportunidades...</span>
              </div>
            </div>
          ) : opportunitiesError ? (
            <div className="flex items-center gap-2 text-destructive py-4">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">{opportunitiesError}</span>
            </div>
          ) : (
            <div className="space-y-2 max-h-80 overflow-y-auto">
              {formatOpportunitiesText(buyOpportunities.opportunities)}
              <div className="flex items-center gap-1 text-xs text-muted-foreground pt-2 border-t">
                <Clock className="h-3 w-3" />
                <span>
                  Actualizado: {new Date(buyOpportunities.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={refreshAll}
          disabled={isLoadingAnalysis || isLoadingOpportunities}
          className="flex-1"
        >
          <RefreshCw className={cn(
            "h-4 w-4 mr-2",
            (isLoadingAnalysis || isLoadingOpportunities) && "animate-spin"
          )} />
          Actualizar Todo
        </Button>
      </div>

      {/* Scheduler Control Panel */}
      {showSchedulerControls && (
        <div className="mt-4">
          <SchedulerControlPanel />
        </div>
      )}
    </div>
  )
}
