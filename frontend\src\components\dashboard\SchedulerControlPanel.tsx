import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { useMarketAnalysis } from '@/hooks/useMarketAnalysis'
import { cn } from '@/utils/cn'
import { 
  Play, 
  Pause, 
  RefreshCw, 
  Settings, 
  Clock,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface SchedulerControlPanelProps {
  className?: string
}

export const SchedulerControlPanel: React.FC<SchedulerControlPanelProps> = ({ 
  className 
}) => {
  const {
    schedulerStatus,
    isLoadingScheduler,
    schedulerError,
    startScheduler,
    stopScheduler,
    getSchedulerStatus,
    triggerUpdate,
    isLoadingAnalysis
  } = useMarketAnalysis()

  const [updateParams, setUpdateParams] = useState({
    batch_size: 10,
    specific_symbols: ''
  })

  // Auto-fetch scheduler status on mount and periodically
  useEffect(() => {
    getSchedulerStatus()
    
    // Poll status every 30 seconds
    const interval = setInterval(getSchedulerStatus, 30000)
    return () => clearInterval(interval)
  }, [getSchedulerStatus])

  const getStatusIcon = () => {
    switch (schedulerStatus) {
      case 'running':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'stopped':
        return <XCircle className="h-4 w-4 text-gray-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />
    }
  }

  const getStatusBadgeVariant = () => {
    switch (schedulerStatus) {
      case 'running':
        return 'success'
      case 'stopped':
        return 'secondary'
      case 'error':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const handleManualUpdate = async () => {
    const params = {
      batch_size: updateParams.batch_size,
      ...(updateParams.specific_symbols && { specific_symbols: updateParams.specific_symbols })
    }
    await triggerUpdate(params)
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Control del Scheduler</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={getSchedulerStatus}
            disabled={isLoadingScheduler}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn(
              "h-4 w-4",
              isLoadingScheduler && "animate-spin"
            )} />
          </Button>
        </div>
        <CardDescription>
          Gestión automática de actualizaciones de datos
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status Section */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="font-medium">Estado:</span>
          </div>
          <Badge variant={getStatusBadgeVariant()}>
            {schedulerStatus.toUpperCase()}
          </Badge>
        </div>

        {/* Error Display */}
        {schedulerError && (
          <div className="flex items-center gap-2 text-destructive p-3 bg-destructive/10 rounded-lg">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{schedulerError}</span>
          </div>
        )}

        {/* Control Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={startScheduler}
            disabled={isLoadingScheduler || schedulerStatus === 'running'}
            variant={schedulerStatus === 'running' ? 'secondary' : 'default'}
            size="sm"
          >
            <Play className="h-4 w-4 mr-2" />
            Iniciar
          </Button>
          
          <Button
            onClick={stopScheduler}
            disabled={isLoadingScheduler || schedulerStatus === 'stopped'}
            variant={schedulerStatus === 'stopped' ? 'secondary' : 'outline'}
            size="sm"
          >
            <Pause className="h-4 w-4 mr-2" />
            Detener
          </Button>
        </div>

        {/* Manual Update Section */}
        <div className="border-t pt-4">
          <div className="flex items-center gap-2 mb-3">
            <Settings className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Actualización Manual</span>
          </div>
          
          <div className="space-y-3">
            {/* Batch Size */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-muted-foreground min-w-[80px]">
                Lote:
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={updateParams.batch_size}
                onChange={(e) => setUpdateParams(prev => ({
                  ...prev,
                  batch_size: parseInt(e.target.value) || 10
                }))}
                className="flex-1 px-2 py-1 text-xs border rounded"
              />
            </div>
            
            {/* Specific Symbols */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-muted-foreground min-w-[80px]">
                Símbolos:
              </label>
              <input
                type="text"
                placeholder="AAPL,TSLA,BTC (opcional)"
                value={updateParams.specific_symbols}
                onChange={(e) => setUpdateParams(prev => ({
                  ...prev,
                  specific_symbols: e.target.value
                }))}
                className="flex-1 px-2 py-1 text-xs border rounded"
              />
            </div>
            
            {/* Manual Update Button */}
            <Button
              onClick={handleManualUpdate}
              disabled={isLoadingAnalysis}
              variant="outline"
              size="sm"
              className="w-full"
            >
              {isLoadingAnalysis ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Actualizando...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Actualizar Ahora
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Info Section */}
        <div className="text-xs text-muted-foreground space-y-1 pt-2 border-t">
          <div className="flex justify-between">
            <span>Intervalo:</span>
            <span>15 minutos</span>
          </div>
          <div className="flex justify-between">
            <span>Horario:</span>
            <span>9:00-16:00 (L-V)</span>
          </div>
          <div className="flex justify-between">
            <span>Cripto:</span>
            <span>24/7</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
