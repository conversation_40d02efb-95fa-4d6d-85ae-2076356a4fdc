// Application constants
export const APP_NAME = 'TradingIA'
export const APP_DESCRIPTION = 'Asistente Financiero IA'
export const APP_VERSION = '1.0.0'

// API endpoints
export const API_ENDPOINTS = {
  CHAT: '/api/v1/chat/',
  CHAT_HEALTH: '/api/v1/chat/health',
  HEALTH: '/health',
} as const

// Route paths
export const ROUTES = {
  HOME: '/',
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
  ACCOUNT: '/account',
  NOT_FOUND: '/404',
} as const

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth-token',
  USER_PREFERENCES: 'user-preferences',
  CHAT_HISTORY: 'chat-history',
  THEME: 'theme',
} as const

// Chat configuration
export const CHAT_CONFIG = {
  MAX_MESSAGE_LENGTH: 2000,
  MAX_HISTORY_LENGTH: 50,
  TYPING_DELAY: 1000,
  MESSAGE_TIMEOUT: 90000, // Increased to 60 seconds for AI response time
  RETRY_ATTEMPTS: 3,
} as const

// UI configuration
export const UI_CONFIG = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DESKTOP_BREAKPOINT: 1280,
} as const

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const

// Chart configuration
export const CHART_CONFIG = {
  DEFAULT_HEIGHT: 400,
  MIN_HEIGHT: 200,
  MAX_HEIGHT: 800,
  DEFAULT_TIMEFRAME: '1D',
  SUPPORTED_TIMEFRAMES: ['1m', '5m', '15m', '30m', '1h', '4h', '1D', '1W', '1M'],
  SUPPORTED_CHART_TYPES: ['line', 'candlestick', 'area'],
} as const

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Error de conexión. Por favor, verifica tu conexión a internet.',
  AUTH_ERROR: 'Error de autenticación. Por favor, inicia sesión nuevamente.',
  VALIDATION_ERROR: 'Por favor, verifica los datos ingresados.',
  GENERIC_ERROR: 'Ha ocurrido un error inesperado. Por favor, intenta nuevamente.',
  CHAT_ERROR: 'Error al enviar el mensaje. Por favor, intenta nuevamente.',
  LOAD_ERROR: 'Error al cargar los datos. Por favor, recarga la página.',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '¡Bienvenido de vuelta!',
  REGISTER_SUCCESS: '¡Cuenta creada exitosamente!',
  LOGOUT_SUCCESS: '¡Sesión cerrada exitosamente!',
  MESSAGE_SENT: 'Mensaje enviado',
  DATA_SAVED: 'Datos guardados exitosamente',
} as const

// Validation rules
export const VALIDATION_RULES = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MESSAGE: 'Por favor, ingresa un email válido',
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    MESSAGE: 'La contraseña debe tener al menos 8 caracteres, una mayúscula, una minúscula y un número',
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-ZÀ-ÿ\s]+$/,
    MESSAGE: 'El nombre debe contener solo letras y espacios',
  },
} as const

// Theme configuration
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const

// File size limits
export const FILE_LIMITS = {
  MAX_AVATAR_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
} as const

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const
