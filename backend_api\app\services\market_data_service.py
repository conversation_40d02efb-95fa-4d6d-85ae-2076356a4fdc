"""
Market Data Service - Gestiona datos de mercado en base de datos
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from app.tools.tradingview_provider import get_price_data, apply_indicator
from app.services.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)

# Activos populares para pre-cargar - LISTA EXPANDIDA
POPULAR_ASSETS = [
    # === TECNOLOGÍA (NASDAQ) ===
    {"symbol": "NASDAQ:AAPL", "name": "Apple Inc."},
    {"symbol": "NASDAQ:MSFT", "name": "Microsoft Corp."},
    {"symbol": "NASDAQ:GOOGL", "name": "Alphabet Inc."},
    {"symbol": "NASDAQ:AMZN", "name": "Amazon.com Inc."},
    {"symbol": "NASDAQ:NVDA", "name": "NVIDIA Corp."},
    {"symbol": "NASDAQ:TSLA", "name": "Tesla Inc."},
    {"symbol": "NASDAQ:META", "name": "Meta Platforms Inc."},
    {"symbol": "NASDAQ:NFLX", "name": "Netflix Inc."},
    {"symbol": "NASDAQ:ADBE", "name": "Adobe Inc."},
    {"symbol": "NASDAQ:CRM", "name": "Salesforce Inc."},
    {"symbol": "NASDAQ:ORCL", "name": "Oracle Corp."},
    {"symbol": "NASDAQ:INTC", "name": "Intel Corp."},
    {"symbol": "NASDAQ:AMD", "name": "Advanced Micro Devices"},
    {"symbol": "NASDAQ:PYPL", "name": "PayPal Holdings"},
    {"symbol": "NASDAQ:UBER", "name": "Uber Technologies"},
    {"symbol": "NASDAQ:ZOOM", "name": "Zoom Video Communications"},
    {"symbol": "NASDAQ:SPOT", "name": "Spotify Technology"},
    {"symbol": "NASDAQ:SHOP", "name": "Shopify Inc."},

    # === ÍNDICES PRINCIPALES ===
    {"symbol": "NASDAQ:QQQ", "name": "Invesco QQQ Trust"},
    {"symbol": "NYSE:SPY", "name": "SPDR S&P 500 ETF"},
    {"symbol": "NYSE:DIA", "name": "SPDR Dow Jones Industrial Average ETF"},
    {"symbol": "NYSE:IWM", "name": "iShares Russell 2000 ETF"},

    # === FINANZAS Y BANCOS ===
    {"symbol": "NYSE:JPM", "name": "JPMorgan Chase & Co."},
    {"symbol": "NYSE:BAC", "name": "Bank of America Corp."},
    {"symbol": "NYSE:WFC", "name": "Wells Fargo & Co."},
    {"symbol": "NYSE:GS", "name": "Goldman Sachs Group"},
    {"symbol": "NYSE:MS", "name": "Morgan Stanley"},
    {"symbol": "NYSE:V", "name": "Visa Inc."},
    {"symbol": "NYSE:MA", "name": "Mastercard Inc."},

    # === ENERGÍA Y MATERIALES ===
    {"symbol": "NYSE:XOM", "name": "Exxon Mobil Corp."},
    {"symbol": "NYSE:CVX", "name": "Chevron Corp."},
    {"symbol": "NYSE:COP", "name": "ConocoPhillips"},
    {"symbol": "NYSE:SLB", "name": "Schlumberger Ltd."},

    # === SALUD Y FARMACÉUTICAS ===
    {"symbol": "NYSE:JNJ", "name": "Johnson & Johnson"},
    {"symbol": "NYSE:PFE", "name": "Pfizer Inc."},
    {"symbol": "NASDAQ:MRNA", "name": "Moderna Inc."},
    {"symbol": "NYSE:UNH", "name": "UnitedHealth Group"},
    {"symbol": "NYSE:ABT", "name": "Abbott Laboratories"},

    # === CONSUMO Y RETAIL ===
    {"symbol": "NYSE:KO", "name": "Coca-Cola Co."},
    {"symbol": "NYSE:PEP", "name": "PepsiCo Inc."},
    {"symbol": "NYSE:WMT", "name": "Walmart Inc."},
    {"symbol": "NYSE:HD", "name": "Home Depot Inc."},
    {"symbol": "NYSE:MCD", "name": "McDonald's Corp."},
    {"symbol": "NYSE:NKE", "name": "Nike Inc."},
    {"symbol": "NYSE:DIS", "name": "Walt Disney Co."},

    # === CRIPTOMONEDAS PRINCIPALES ===
    {"symbol": "BINANCE:BTCUSDT", "name": "Bitcoin"},
    {"symbol": "BINANCE:ETHUSDT", "name": "Ethereum"},
    {"symbol": "BINANCE:BNBUSDT", "name": "Binance Coin"},
    {"symbol": "BINANCE:ADAUSDT", "name": "Cardano"},
    {"symbol": "BINANCE:SOLUSDT", "name": "Solana"},
    {"symbol": "BINANCE:XRPUSDT", "name": "XRP"},
    {"symbol": "BINANCE:DOTUSDT", "name": "Polkadot"},
    {"symbol": "BINANCE:AVAXUSDT", "name": "Avalanche"},
    {"symbol": "BINANCE:LINKUSDT", "name": "Chainlink"},
    {"symbol": "BINANCE:MATICUSDT", "name": "Polygon"},
    {"symbol": "BINANCE:UNIUSDT", "name": "Uniswap"},
    {"symbol": "BINANCE:LTCUSDT", "name": "Litecoin"},
    {"symbol": "BINANCE:BCHUSDT", "name": "Bitcoin Cash"},
    {"symbol": "BINANCE:ETCUSDT", "name": "Ethereum Classic"},
    {"symbol": "BINANCE:XLMUSDT", "name": "Stellar"},
    {"symbol": "BINANCE:VETUSDT", "name": "VeChain"},
    {"symbol": "BINANCE:TRXUSDT", "name": "TRON"},
    {"symbol": "BINANCE:EOSUSDT", "name": "EOS"},
    {"symbol": "BINANCE:XMRUSDT", "name": "Monero"},
    {"symbol": "BINANCE:DASHUSDT", "name": "Dash"},

    # === COMMODITIES Y METALES ===
    {"symbol": "COMEX:GC1!", "name": "Gold Futures"},
    {"symbol": "COMEX:SI1!", "name": "Silver Futures"},
    {"symbol": "NYMEX:CL1!", "name": "Crude Oil Futures"},
    {"symbol": "CBOT:ZC1!", "name": "Corn Futures"},
    {"symbol": "CBOT:ZW1!", "name": "Wheat Futures"},

    # === FOREX PRINCIPALES ===
    {"symbol": "FX:EURUSD", "name": "Euro/US Dollar"},
    {"symbol": "FX:GBPUSD", "name": "British Pound/US Dollar"},
    {"symbol": "FX:USDJPY", "name": "US Dollar/Japanese Yen"},
    {"symbol": "FX:USDCHF", "name": "US Dollar/Swiss Franc"},
    {"symbol": "FX:AUDUSD", "name": "Australian Dollar/US Dollar"},
    {"symbol": "FX:USDCAD", "name": "US Dollar/Canadian Dollar"},
]

# Indicadores técnicos a calcular - EXPANDIDO
TECHNICAL_INDICATORS = [
    "RSI",      # Índice de Fuerza Relativa
    "MACD",     # Convergencia/Divergencia de Medias Móviles
    "SMA",      # Media Móvil Simple
    "EMA",      # Media Móvil Exponencial
    "BBANDS",   # Bandas de Bollinger
    "STOCH",    # Oscilador Estocástico
    "ATR",      # Average True Range (volatilidad)
    "ADX"       # Average Directional Index (fuerza de tendencia)
]

class MarketDataService:
    """Servicio para gestionar datos de mercado en base de datos"""
    
    def __init__(self):
        self.supabase = get_supabase_client()
    
    async def update_market_data(self, batch_size: int = 10) -> Dict[str, Any]:
        """
        Actualiza datos de mercado para todos los activos populares
        Procesa en lotes para evitar sobrecarga
        """
        results = {
            "updated_assets": 0,
            "updated_indicators": 0,
            "errors": [],
            "total_assets": len(POPULAR_ASSETS)
        }

        logger.info(f"🔄 Iniciando actualización de {len(POPULAR_ASSETS)} activos en lotes de {batch_size}...")

        # Procesar en lotes para evitar sobrecarga
        for i in range(0, len(POPULAR_ASSETS), batch_size):
            batch = POPULAR_ASSETS[i:i + batch_size]
            logger.info(f"📦 Procesando lote {i//batch_size + 1}/{(len(POPULAR_ASSETS) + batch_size - 1)//batch_size}")

            for asset in batch:
                try:
                    # Obtener datos de precio
                    price_data = await self._get_asset_price_data(asset["symbol"])
                    if price_data:
                        await self._save_market_data(asset["symbol"], asset["name"], price_data)
                        results["updated_assets"] += 1
                        logger.debug(f"✅ {asset['symbol']}: Precio actualizado")

                        # Calcular indicadores técnicos (solo para activos principales)
                        if self._is_main_asset(asset["symbol"]):
                            for indicator in TECHNICAL_INDICATORS:
                                try:
                                    indicator_data = await self._calculate_indicator(asset["symbol"], indicator)
                                    if indicator_data:
                                        await self._save_indicator_data(asset["symbol"], indicator, indicator_data)
                                        results["updated_indicators"] += 1
                                except Exception as e:
                                    logger.warning(f"Error calculando {indicator} para {asset['symbol']}: {e}")
                                    results["errors"].append(f"{asset['symbol']}-{indicator}: {str(e)}")
                    else:
                        logger.warning(f"⚠️ No se pudieron obtener datos para {asset['symbol']}")
                        results["errors"].append(f"{asset['symbol']}: No data available")

                except Exception as e:
                    logger.error(f"Error actualizando {asset['symbol']}: {e}")
                    results["errors"].append(f"{asset['symbol']}: {str(e)}")

            # Pequeña pausa entre lotes para no sobrecargar las APIs
            if i + batch_size < len(POPULAR_ASSETS):
                await asyncio.sleep(1)

        logger.info(f"✅ Actualización completada: {results}")
        return results

    async def update_specific_assets(self, symbols: List[str], batch_size: int = 10) -> Dict[str, Any]:
        """
        Actualiza solo los símbolos específicos proporcionados

        Args:
            symbols: Lista de símbolos a actualizar (ej: ['BINANCE:BTCUSDT', 'NASDAQ:AAPL'])
            batch_size: Tamaño del lote para procesamiento
        """
        try:
            logger.info(f"🎯 Iniciando actualización de símbolos específicos: {symbols}")

            results = {
                "updated_assets": 0,
                "updated_indicators": 0,
                "errors": [],
                "total_symbols": len(symbols),
                "processed_symbols": []
            }

            # Crear diccionario de activos por símbolo para búsqueda rápida
            assets_by_symbol = {asset["symbol"]: asset for asset in POPULAR_ASSETS}

            # Validar que los símbolos existen en nuestra lista
            valid_assets = []
            for symbol in symbols:
                if symbol in assets_by_symbol:
                    valid_assets.append(assets_by_symbol[symbol])
                    logger.info(f"✅ Símbolo válido: {symbol}")
                else:
                    logger.warning(f"⚠️ Símbolo no reconocido: {symbol}")
                    results["errors"].append(f"{symbol}: Símbolo no está en la lista de activos configurados")

            if not valid_assets:
                logger.warning("❌ No se encontraron símbolos válidos")
                return results

            logger.info(f"🎯 Procesando {len(valid_assets)} símbolos válidos")

            # Procesar símbolos válidos en lotes
            for i in range(0, len(valid_assets), batch_size):
                batch = valid_assets[i:i + batch_size]
                logger.info(f"📦 Procesando lote específico {i//batch_size + 1}: {[asset['symbol'] for asset in batch]}")

                for asset in batch:
                    try:
                        symbol = asset["symbol"]
                        logger.info(f"🔄 Actualizando {symbol}...")

                        # Obtener datos de precio
                        price_data = await self._get_asset_price_data(symbol)
                        if price_data:
                            await self._save_market_data(symbol, asset["name"], price_data)
                            results["updated_assets"] += 1
                            results["processed_symbols"].append(symbol)
                            logger.info(f"✅ {symbol}: Precio actualizado")

                            # Calcular indicadores técnicos (solo para activos principales)
                            if self._is_main_asset(symbol):
                                for indicator in TECHNICAL_INDICATORS:
                                    try:
                                        indicator_data = await self._calculate_indicator(symbol, indicator)
                                        if indicator_data:
                                            await self._save_indicator_data(symbol, indicator, indicator_data)
                                            results["updated_indicators"] += 1
                                            logger.debug(f"✅ {symbol}: {indicator} calculado")
                                    except Exception as e:
                                        logger.warning(f"Error calculando {indicator} para {symbol}: {e}")
                                        results["errors"].append(f"{symbol}-{indicator}: {str(e)}")
                            else:
                                logger.debug(f"ℹ️ {symbol}: No es activo principal, saltando indicadores")
                        else:
                            logger.warning(f"⚠️ No se pudieron obtener datos para {symbol}")
                            results["errors"].append(f"{symbol}: No data available")

                        # Pequeña pausa para evitar rate limiting
                        await asyncio.sleep(0.2)

                    except Exception as e:
                        logger.error(f"❌ Error actualizando {asset['symbol']}: {e}")
                        results["errors"].append(f"{asset['symbol']}: {str(e)}")

                # Pausa entre lotes
                if i + batch_size < len(valid_assets):
                    logger.info(f"⏸️ Pausa entre lotes específicos...")
                    await asyncio.sleep(0.5)

            logger.info(f"🎯 Actualización específica completada: {results}")
            return results

        except Exception as e:
            logger.error(f"❌ Error en actualización específica: {e}")
            raise

    def _is_main_asset(self, symbol: str) -> bool:
        """Determina si un activo es principal para calcular indicadores"""
        main_assets = [
            "NASDAQ:AAPL", "NASDAQ:TSLA", "NASDAQ:MSFT", "NASDAQ:GOOGL",
            "NASDAQ:AMZN", "NASDAQ:NVDA", "NASDAQ:META", "NASDAQ:NFLX",
            "BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT",
            "BINANCE:ADAUSDT", "BINANCE:SOLUSDT", "BINANCE:XRPUSDT"
        ]
        return symbol in main_assets
    
    async def _get_asset_price_data(self, symbol: str) -> Optional[Dict]:
        """Obtiene datos de precio para un activo"""
        try:
            data = get_price_data(symbol, "1D", 5)
            if data and "latest_price" in data:
                return data
        except Exception as e:
            logger.error(f"Error obteniendo precio de {symbol}: {e}")
        return None
    
    async def _calculate_indicator(self, symbol: str, indicator: str) -> Optional[Dict]:
        """Calcula un indicador técnico para un activo"""
        try:
            params = {"length": 14} if indicator in ["RSI", "SMA", "EMA"] else {}
            data = apply_indicator(symbol, "1D", indicator, params)
            if data and "latest_value" in data:
                return data
        except Exception as e:
            logger.error(f"Error calculando {indicator} para {symbol}: {e}")
        return None
    
    async def _save_market_data(self, symbol: str, name: str, price_data: Dict):
        """Guarda datos de mercado en la base de datos"""
        try:
            latest_price = price_data.get("latest_price")
            data_points = price_data.get("data", [])
            
            # Calcular cambio si hay datos históricos
            change_24h = 0
            change_percent_24h = 0
            if len(data_points) >= 2:
                current = data_points[-1].get("close", latest_price)
                previous = data_points[-2].get("close", current)
                change_24h = current - previous
                change_percent_24h = (change_24h / previous) * 100 if previous != 0 else 0
            
            market_data = {
                "symbol": symbol,
                "name": name,
                "current_price": latest_price,
                "change_24h": change_24h,
                "change_percent_24h": change_percent_24h,
                "last_updated": datetime.now().isoformat()
            }
            
            # Upsert (insert or update)
            result = self.supabase.table("market_data").upsert(
                market_data, 
                on_conflict="symbol"
            ).execute()
            
            logger.debug(f"💾 Guardado precio de {symbol}: ${latest_price}")
            
        except Exception as e:
            logger.error(f"Error guardando datos de {symbol}: {e}")
            raise
    
    async def _save_indicator_data(self, symbol: str, indicator: str, indicator_data: Dict):
        """Guarda datos de indicador técnico en la base de datos"""
        try:
            latest_value = indicator_data.get("latest_value", {})
            value = latest_value.get("value") if latest_value else None
            
            if value is not None:
                # Generar interpretación
                interpretation = self._generate_interpretation(indicator, value)
                
                technical_data = {
                    "symbol": symbol,
                    "indicator_name": indicator,
                    "value": value,
                    "interpretation": interpretation,
                    "calculated_at": datetime.now().isoformat()
                }
                
                # Upsert (insert or update)
                result = self.supabase.table("technical_indicators").upsert(
                    technical_data,
                    on_conflict="symbol,indicator_name"
                ).execute()
                
                logger.debug(f"💾 Guardado {indicator} de {symbol}: {value}")
                
        except Exception as e:
            logger.error(f"Error guardando {indicator} de {symbol}: {e}")
            raise
    
    def _generate_interpretation(self, indicator: str, value: float) -> str:
        """Genera interpretación textual avanzada de un indicador"""
        indicator = indicator.upper()

        if indicator == "RSI":
            if value > 80:
                return "Sobrecompra extrema - Señal de venta fuerte"
            elif value > 70:
                return "Sobrecompra - Posible señal de venta"
            elif value > 50:
                return "Zona alcista - Momentum positivo"
            elif value > 30:
                return "Zona neutral - Sin señales claras"
            elif value > 20:
                return "Sobreventa - Posible señal de compra"
            else:
                return "Sobreventa extrema - Señal de compra fuerte"

        elif indicator == "MACD":
            if value > 2:
                return "Momentum alcista fuerte"
            elif value > 0:
                return "Tendencia alcista"
            elif value > -2:
                return "Tendencia bajista"
            else:
                return "Momentum bajista fuerte"

        elif indicator == "STOCH":
            if value > 80:
                return "Sobrecompra - Considerar venta"
            elif value < 20:
                return "Sobreventa - Considerar compra"
            else:
                return "Zona neutral"

        elif indicator == "ATR":
            if value > 5:
                return "Alta volatilidad - Mayor riesgo"
            elif value > 2:
                return "Volatilidad moderada"
            else:
                return "Baja volatilidad - Mercado estable"

        elif indicator == "ADX":
            if value > 50:
                return "Tendencia muy fuerte"
            elif value > 25:
                return "Tendencia fuerte"
            elif value > 20:
                return "Tendencia moderada"
            else:
                return "Sin tendencia clara - Mercado lateral"

        elif indicator in ["SMA", "EMA"]:
            return f"Media móvil: ${value:.2f}"

        elif indicator == "BBANDS":
            return f"Banda de Bollinger: ${value:.2f}"

        else:
            return f"Valor: {value:.2f}"
    
    async def get_market_context_for_ai(self) -> str:
        """
        Obtiene contexto de mercado para incluir en el system instruction de la IA
        """
        try:
            # Obtener datos recientes (últimas 2 horas)
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()
            
            # Datos de mercado
            market_result = self.supabase.table("market_data").select("*").gte(
                "last_updated", cutoff_time
            ).execute()
            
            # Indicadores técnicos
            indicators_result = self.supabase.table("technical_indicators").select("*").gte(
                "calculated_at", cutoff_time
            ).execute()
            
            # Formatear contexto OPTIMIZADO para muchos activos
            context = "DATOS DE MERCADO ACTUALES:\n\n"

            if market_result.data:
                # Categorizar activos por tipo
                tech_stocks = [item for item in market_result.data if "NASDAQ:" in item["symbol"]][:8]
                crypto = [item for item in market_result.data if "BINANCE:" in item["symbol"]][:6]
                indices = [item for item in market_result.data if item["symbol"] in ["NASDAQ:QQQ", "NYSE:SPY", "NYSE:DIA"]]
                forex = [item for item in market_result.data if "FX:" in item["symbol"]][:3]

                if tech_stocks:
                    context += "TECNOLOGÍA: "
                    for item in tech_stocks:
                        symbol = item["symbol"].replace("NASDAQ:", "")
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        context += f"{symbol}:${price:.0f}({change:+.1f}%) "
                    context += "\n"

                if crypto:
                    context += "CRIPTO: "
                    for item in crypto:
                        symbol = item["symbol"].replace("BINANCE:", "").replace("USDT", "")
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        if price > 1:
                            context += f"{symbol}:${price:.0f}({change:+.1f}%) "
                        else:
                            context += f"{symbol}:${price:.3f}({change:+.1f}%) "
                    context += "\n"

                if indices:
                    context += "ÍNDICES: "
                    for item in indices:
                        symbol = item["symbol"].split(":")[-1]
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        context += f"{symbol}:${price:.0f}({change:+.1f}%) "
                    context += "\n"

                if forex:
                    context += "FOREX: "
                    for item in forex:
                        symbol = item["symbol"].replace("FX:", "")
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        context += f"{symbol}:{price:.4f}({change:+.1f}%) "
                    context += "\n"

            if indicators_result.data:
                # Organizar indicadores por símbolo para activos principales
                main_symbols = ["NASDAQ:AAPL", "NASDAQ:TSLA", "NASDAQ:MSFT", "NASDAQ:GOOGL",
                               "NASDAQ:AMZN", "NASDAQ:NVDA", "NASDAQ:META", "NASDAQ:NFLX",
                               "BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT",
                               "BINANCE:ADAUSDT", "BINANCE:SOLUSDT", "BINANCE:XRPUSDT"]

                indicators_by_symbol = {}
                for indicator in indicators_result.data:
                    if indicator["symbol"] in main_symbols:
                        symbol = indicator["symbol"]
                        if symbol not in indicators_by_symbol:
                            indicators_by_symbol[symbol] = {}
                        indicators_by_symbol[symbol][indicator["indicator_name"]] = indicator["value"]

                # Mostrar indicadores técnicos completos
                context += "\nINDICADORES TÉCNICOS:\n"

                for symbol, indicators in indicators_by_symbol.items():
                    clean_symbol = symbol.replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                    context += f"{clean_symbol}: "

                    # RSI (Sobrecompra/Sobreventa)
                    if "RSI" in indicators:
                        rsi = indicators["RSI"]
                        if rsi > 70:
                            context += f"RSI:{rsi:.0f}(sobrecompra) "
                        elif rsi < 30:
                            context += f"RSI:{rsi:.0f}(sobreventa) "
                        else:
                            context += f"RSI:{rsi:.0f} "

                    # MACD (Momentum)
                    if "MACD" in indicators:
                        macd = indicators["MACD"]
                        if macd > 0:
                            context += f"MACD:{macd:.1f}(alcista) "
                        else:
                            context += f"MACD:{macd:.1f}(bajista) "

                    # Estocástico (Momentum)
                    if "STOCH" in indicators:
                        stoch = indicators["STOCH"]
                        if stoch > 80:
                            context += f"STOCH:{stoch:.0f}(sobrecompra) "
                        elif stoch < 20:
                            context += f"STOCH:{stoch:.0f}(sobreventa) "
                        else:
                            context += f"STOCH:{stoch:.0f} "

                    # ATR (Volatilidad)
                    if "ATR" in indicators:
                        atr = indicators["ATR"]
                        if atr > 5:
                            context += f"ATR:{atr:.1f}(alta_vol) "
                        elif atr > 2:
                            context += f"ATR:{atr:.1f}(mod_vol) "
                        else:
                            context += f"ATR:{atr:.1f}(baja_vol) "

                    # ADX (Fuerza de tendencia)
                    if "ADX" in indicators:
                        adx = indicators["ADX"]
                        if adx > 25:
                            context += f"ADX:{adx:.0f}(tendencia_fuerte) "
                        else:
                            context += f"ADX:{adx:.0f}(sin_tendencia) "

                    # Medias móviles (solo mostrar una para no saturar)
                    if "SMA" in indicators:
                        sma = indicators["SMA"]
                        context += f"SMA:{sma:.0f} "

                    context += "\n"

            context += f"Actualizado: {datetime.now().strftime('%H:%M')}\n"
            context += f"Total activos: {len(market_result.data) if market_result.data else 0}\n"
            
            return context
            
        except Exception as e:
            logger.error(f"Error obteniendo contexto de mercado: {e}")
            return "Datos de mercado no disponibles en este momento.\n"

    async def generate_market_analysis(self) -> str:
        """
        Genera análisis general de mercado con tendencias y alertas
        """
        try:
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()

            # Obtener datos recientes
            market_result = self.supabase.table("market_data").select("*").gte(
                "last_updated", cutoff_time
            ).execute()

            indicators_result = self.supabase.table("technical_indicators").select("*").gte(
                "calculated_at", cutoff_time
            ).execute()

            if not market_result.data:
                return "No hay datos de mercado disponibles para el análisis."

            # Análisis de tendencia general
            positive_changes = len([item for item in market_result.data if item["change_percent_24h"] > 0])
            total_assets = len(market_result.data)
            market_sentiment = "Alcista" if positive_changes > total_assets * 0.6 else "Bajista" if positive_changes < total_assets * 0.4 else "Neutral"

            # Mejores y peores performers
            sorted_by_change = sorted(market_result.data, key=lambda x: x["change_percent_24h"], reverse=True)
            top_gainers = sorted_by_change[:3]
            top_losers = sorted_by_change[-3:]

            # Análisis de indicadores
            rsi_data = [item for item in indicators_result.data if item["indicator_name"] == "RSI"]
            overbought = len([item for item in rsi_data if item["value"] > 70])
            oversold = len([item for item in rsi_data if item["value"] < 30])

            macd_data = [item for item in indicators_result.data if item["indicator_name"] == "MACD"]
            strong_momentum = len([item for item in macd_data if item["value"] > 2])

            # Generar análisis
            analysis = f"""📊 ANÁLISIS GENERAL DE MERCADO - {datetime.now().strftime('%d %B %Y, %H:%M')}

🎯 TENDENCIA GENERAL: {market_sentiment}
• {positive_changes}/{total_assets} activos en positivo ({positive_changes/total_assets*100:.0f}%)

🚀 MEJORES PERFORMERS:"""

            for gainer in top_gainers:
                symbol = gainer["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                analysis += f"\n• {symbol}: {gainer['change_percent_24h']:+.1f}%"

            analysis += f"\n\n📉 MAYORES CAÍDAS:"
            for loser in top_losers:
                symbol = loser["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                analysis += f"\n• {symbol}: {loser['change_percent_24h']:+.1f}%"

            analysis += f"""

⚠️ ALERTAS TÉCNICAS:
• {overbought} activos en sobrecompra (RSI >70)
• {oversold} activos en sobreventa (RSI <30)
• {strong_momentum} activos con momentum alcista fuerte (MACD >2)

📈 Total activos monitoreados: {total_assets}
🕐 Última actualización: {datetime.now().strftime('%H:%M')}

Esta información es solo para fines educativos y no constituye asesoramiento financiero."""

            return analysis

        except Exception as e:
            logger.error(f"Error generando análisis de mercado: {e}")
            return "Error al generar el análisis de mercado."

    async def generate_buy_opportunities(self) -> str:
        """
        Analiza todos los activos y genera recomendaciones de compra
        basadas en múltiples indicadores técnicos
        """
        try:
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()

            # Obtener datos recientes
            market_result = self.supabase.table("market_data").select("*").gte(
                "last_updated", cutoff_time
            ).execute()

            indicators_result = self.supabase.table("technical_indicators").select("*").gte(
                "calculated_at", cutoff_time
            ).execute()

            if not market_result.data or not indicators_result.data:
                return "No hay suficientes datos para generar recomendaciones."

            # Organizar indicadores por símbolo
            indicators_by_symbol = {}
            for indicator in indicators_result.data:
                symbol = indicator["symbol"]
                if symbol not in indicators_by_symbol:
                    indicators_by_symbol[symbol] = {}
                indicators_by_symbol[symbol][indicator["indicator_name"]] = indicator["value"]

            # Analizar cada activo
            high_priority = []
            medium_priority = []
            avoid_list = []

            for asset in market_result.data:
                symbol = asset["symbol"]
                name = asset["name"]
                price = asset["current_price"]
                change = asset["change_percent_24h"]

                # Obtener indicadores
                indicators = indicators_by_symbol.get(symbol, {})
                rsi = indicators.get("RSI")
                macd = indicators.get("MACD")
                stoch = indicators.get("STOCH")

                if not rsi:  # Skip si no hay indicadores
                    continue

                # Lógica de scoring
                score = 0
                reasons = []

                # RSI scoring
                if rsi < 30:
                    score += 3
                    reasons.append("RSI sobreventa")
                elif 30 <= rsi <= 50:
                    score += 2
                    reasons.append("RSI neutral-alcista")
                elif 50 < rsi <= 65:
                    score += 1
                    reasons.append("RSI zona alcista")
                elif rsi > 70:
                    score -= 2
                    reasons.append("RSI sobrecompra")

                # MACD scoring
                if macd and macd > 0:
                    score += 1
                    reasons.append("MACD positivo")
                elif macd and macd < -1:
                    score -= 1

                # Momentum reciente
                if change > 0:
                    score += 0.5
                elif change < -3:
                    score -= 1

                # Clasificar
                clean_symbol = symbol.replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                asset_info = {
                    "symbol": clean_symbol,
                    "name": name,
                    "price": price,
                    "change": change,
                    "rsi": rsi,
                    "macd": macd,
                    "score": score,
                    "reasons": reasons
                }

                if score >= 3:
                    high_priority.append(asset_info)
                elif score >= 1:
                    medium_priority.append(asset_info)
                elif score < 0:
                    avoid_list.append(asset_info)

            # Ordenar por score
            high_priority.sort(key=lambda x: x["score"], reverse=True)
            medium_priority.sort(key=lambda x: x["score"], reverse=True)
            avoid_list.sort(key=lambda x: x["score"])

            # Generar recomendaciones
            recommendations = f"""🎯 OPORTUNIDADES DE COMPRA DETECTADAS - {datetime.now().strftime('%H:%M')}

🥇 ALTA PRIORIDAD ({len(high_priority)} activos):"""

            for asset in high_priority[:5]:  # Top 5
                recommendations += f"""
• {asset['symbol']} (${asset['price']:.2f}): {asset['change']:+.1f}%
  RSI: {asset['rsi']:.0f} | MACD: {asset['macd']:.2f if asset['macd'] else 'N/A'}
  Razones: {', '.join(asset['reasons'])}"""

            recommendations += f"\n\n🥈 MEDIA PRIORIDAD ({len(medium_priority)} activos):"

            for asset in medium_priority[:5]:  # Top 5
                recommendations += f"""
• {asset['symbol']} (${asset['price']:.2f}): {asset['change']:+.1f}%
  RSI: {asset['rsi']:.0f} | Razones: {', '.join(asset['reasons'])}"""

            if avoid_list:
                recommendations += f"\n\n⚠️ EVITAR POR AHORA ({len(avoid_list)} activos):"
                for asset in avoid_list[:3]:  # Top 3 a evitar
                    recommendations += f"""
• {asset['symbol']}: RSI {asset['rsi']:.0f} ({', '.join(asset['reasons'])})"""

            recommendations += f"""

📋 CRITERIOS DE ANÁLISIS:
• RSI: Sobreventa (<30) = Oportunidad | Sobrecompra (>70) = Evitar
• MACD: Positivo = Momentum alcista
• Cambio reciente: Considerado en scoring

⚠️ IMPORTANTE: Esta información es solo para fines educativos.
Siempre realiza tu propio análisis antes de invertir."""

            return recommendations

        except Exception as e:
            logger.error(f"Error generando oportunidades de compra: {e}")
            return "Error al generar recomendaciones de compra."

# Instancia global del servicio
market_data_service = MarketDataService()
