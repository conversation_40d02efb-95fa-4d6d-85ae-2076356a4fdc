import re
"""
Vertex AI service - Versión corregida y robustecida
Basado en los resultados de debug y análisis del problema con thought_signature y mensajes duplicados.
"""

import logging
import json
import time
import asyncio
from typing import List, Dict, Any, Optional
from fastapi import HTTPException, status
import traceback

# Vertex AI imports
import vertexai
from vertexai.generative_models import GenerativeModel, Content, Part
import vertexai.generative_models as generative_models
from vertexai.generative_models import GenerativeModel, Content, Part

from app.config import settings

logger = logging.getLogger(__name__)

def call_vertex_ai_with_timeout(model, contents_history, timeout_seconds=90):
    """
    Llama a Vertex AI con timeout para evitar esperas indefinidas.

    Args:
        model: GenerativeModel instance
        contents_history: Lista de contenidos para enviar
        timeout_seconds: Timeout en segundos (default: 90)

    Returns:
        Response de Vertex AI

    Raises:
        TimeoutError: Si la llamada tarda más del timeout
        Exception: Otros errores de Vertex AI
    """
    import concurrent.futures

    def make_call():
        return model.generate_content(contents_history)

    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(make_call)
        try:
            return future.result(timeout=timeout_seconds)
        except concurrent.futures.TimeoutError:
            raise TimeoutError(f"Vertex AI call timed out after {timeout_seconds} seconds")

async def initialize_gemini_model_with_market_data() -> Optional[GenerativeModel]:
    """
    Initialize Gemini model with market data context (NO TOOLS)

    Returns:
        GenerativeModel instance or None if initialization fails
    """
    try:
        print(f"🔧 Initializing Vertex AI...")
        print(f"🔧 Project: {settings.vertex_ai_project}")
        print(f"🔧 Location: {settings.vertex_ai_location}")
        
        # Setup Google Cloud credentials from JSON string
        if settings.google_credentials_json:
            from google.oauth2 import service_account

            print("🔐 Setting up Google Cloud credentials from JSON...")

            try:
                credentials_info = json.loads(settings.google_credentials_json)
                if 'private_key' in credentials_info:
                    print("🔐 Replacing literal \n with actual newlines in private_key")
                    credentials_info['private_key'] = re.sub(r'\\n', '\n', credentials_info['private_key'])
            except json.JSONDecodeError as e:
                print(f"🔐 JSON parse failed, trying with newline replacement: {e}")
                credentials_json = settings.google_credentials_json.replace('\\n', '\n')
                credentials_info = json.loads(credentials_json)

            print(f"🔐 Credentials project_id: {credentials_info.get('project_id')}")
            # Added for debugging
            print(f"🔐 Credentials info keys: {credentials_info.keys()}")
            if 'private_key' in credentials_info:
                print("🔐 Private key found.")
                print(f"🔐 Private key starts with: {credentials_info['private_key'][:30]}")
            else:
                print("🔐 Private key not found.")

            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location,
                credentials=credentials
            )
            print("✅ Vertex AI initialized with service account credentials")
        else:
            print("⚠️ No Google credentials JSON found, using default authentication")
            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location
            )
        
        vertex_tools = []
        if tools:
            print(f"🔧 Converting {len(tools)} tools to Vertex AI format...")
            function_declarations = []
            for i, tool in enumerate(tools):
                try:
                    print(f"🔧 Processing tool {i+1}: {tool.get('name', 'unknown')}")
                    if isinstance(tool, dict):
                        func_decl = generative_models.FunctionDeclaration(
                            name=tool["name"],
                            description=tool["description"],
                            parameters=tool["parameters"]
                        )
                        function_declarations.append(func_decl)
                        print(f"✅ Successfully converted tool: {tool['name']}")
                except Exception as e:
                    logger.error(f"Error converting tool {tool}: {e}")
                    print(f"❌ Error converting tool {i+1}: {e}")
                    import traceback
                    print(f"❌ Traceback: {traceback.format_exc()}")

            if function_declarations:
                try:
                    combined_tool = generative_models.Tool(function_declarations=function_declarations)
                    vertex_tools.append(combined_tool)
                    print(f"✅ Combined tool created with {len(function_declarations)} functions")
                except Exception as e:
                    logger.error(f"Error creating combined tool: {e}")
                    print(f"❌ Error creating combined tool: {e}")
                    import traceback
                    print(f"❌ Traceback: {traceback.format_exc()}")

        print(f"🔧 Creating model with {len(vertex_tools)} vertex tools")

        # Verificar que las herramientas estén disponibles
        if not vertex_tools:
            print("⚠️ WARNING: No tools available for the model")
        else:
            try:
                tool_names = []
                for tool in vertex_tools:
                    if hasattr(tool, 'function_declarations') and tool.function_declarations:
                        for func_decl in tool.function_declarations:
                            if hasattr(func_decl, 'name'):
                                tool_names.append(func_decl.name)
                print(f"✅ Tools configured: {tool_names}")
            except Exception as e:
                print(f"⚠️ Error listing tool names: {e}")
                print(f"✅ Tools configured: {len(vertex_tools)} tools available")

        try:
            print(f"🔧 Creating GenerativeModel with {len(vertex_tools)} tools...")

            # Configurar parámetros de generación basado en código funcional TypeScript
            generation_config = {
                "max_output_tokens": 8192,  # Aumentado significativamente como en el código funcional
                "temperature": 0.7,         # Aumentado para más naturalidad en conversaciones
                "top_p": 0.95,             # Ajustado según configuración funcional
            }

            model = GenerativeModel(
                model_name="gemini-2.5-pro",  # Usar un modelo estable recomendado para tools
                tools=vertex_tools if vertex_tools else None,
                generation_config=generation_config,
                system_instruction="""
Eres un asistente financiero experto. Responde SIEMPRE con texto claro y visible en español.

Para preguntas sobre precios de acciones o criptomonedas, usa get_price_data.
Para preguntas sobre indicadores técnicos, usa apply_indicator.
Para otras preguntas financieras, responde directamente.

Incluye siempre: "Esta información es solo para fines educativos."

IMPORTANTE: Tu respuesta debe ser texto legible y útil."""
            )

            print(f"✅ Gemini model initialized successfully with {len(tools)} tools")
            logger.info(f"Gemini model initialized successfully with {len(tools)} tools")
            return model

        except Exception as e:
            logger.error(f"Error creating GenerativeModel: {e}")
            print(f"❌ Error creating GenerativeModel: {e}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return None
        
    except Exception as e:
        logger.error(f"Error initializing Gemini model: {e}")
        print(f"❌ Error initializing Gemini model: {e}")
        return None

def initialize_gemini_model(tools: List[Any]) -> Optional[GenerativeModel]:
    """
    FUNCIÓN ORIGINAL - mantener para compatibilidad
    """
    # Llamar a la nueva función sin herramientas
    import asyncio
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(initialize_gemini_model_with_market_data())
    except:
        # Si no hay loop, crear uno nuevo
        return asyncio.run(initialize_gemini_model_with_market_data())

async def initialize_gemini_model_no_tools() -> Optional[GenerativeModel]:
    """
    Initialize Gemini model WITHOUT tools, using market data from database
    """
    try:
        print(f"🔧 Initializing Vertex AI WITHOUT tools...")
        print(f"🔧 Project: {settings.vertex_ai_project}")
        print(f"🔧 Location: {settings.vertex_ai_location}")

        # Setup Google Cloud credentials
        if settings.google_credentials_json:
            from google.oauth2 import service_account
            print("🔐 Setting up Google Cloud credentials from JSON...")

            try:
                credentials_info = json.loads(settings.google_credentials_json)
                if 'private_key' in credentials_info:
                    credentials_info['private_key'] = credentials_info['private_key'].replace('\\n', '\n')
            except json.JSONDecodeError as e:
                credentials_json = settings.google_credentials_json.replace('\\n', '\n')
                credentials_info = json.loads(credentials_json)

            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location,
                credentials=credentials
            )
            print("✅ Vertex AI initialized with service account credentials")
        else:
            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location
            )

        # Obtener contexto de mercado de la base de datos
        print("📊 Obteniendo datos de mercado de la base de datos...")
        from app.services.market_data_service import market_data_service
        market_context = await market_data_service.get_market_context_for_ai()
        print(f"✅ Contexto de mercado obtenido ({len(market_context)} caracteres)")

        # Configurar parámetros de generación optimizados
        generation_config = {
            "max_output_tokens": 8192,
            "temperature": 0.7,
            "top_p": 0.95,
        }

        # System instruction con datos de mercado incluidos
        system_instruction = f"""Eres un asistente financiero experto especializado en análisis técnico. Responde SIEMPRE con texto claro y visible en español.

{market_context}

INDICADORES TÉCNICOS DISPONIBLES:
Tienes acceso completo a los siguientes indicadores técnicos para los activos principales:

• RSI (Relative Strength Index): Mide sobrecompra (>70) y sobreventa (<30)
• MACD (Moving Average Convergence Divergence): Indica momentum alcista (>0) o bajista (<0)
• STOCH (Stochastic Oscillator): Sobrecompra (>80), sobreventa (<20)
• ATR (Average True Range): Mide volatilidad (alta >5, moderada 2-5, baja <2)
• ADX (Average Directional Index): Fuerza de tendencia (fuerte >25, débil <25)
• SMA (Simple Moving Average): Media móvil para identificar tendencias
• EMA (Exponential Moving Average): Media móvil exponencial más sensible
• BBANDS (Bollinger Bands): Bandas de volatilidad

CAPACIDADES DE ANÁLISIS:
- Análisis técnico completo usando múltiples indicadores
- Identificación de señales de compra/venta
- Evaluación de momentum y tendencias
- Análisis de volatilidad y riesgo
- Comparación entre activos y sectores
- Detección de divergencias y patrones

INSTRUCCIONES:
- Usa TODOS los indicadores disponibles para análisis completos
- Combina múltiples indicadores para señales más robustas
- Explica las razones técnicas detrás de tus análisis
- Proporciona contexto educativo sobre los indicadores
- Siempre incluye: "Esta información es solo para fines educativos"
- Responde de manera natural y conversacional

IMPORTANTE: Tienes acceso a datos completos de indicadores técnicos, úsalos para análisis profundos y precisos."""

        model = GenerativeModel(
            model_name="gemini-2.5-pro",
            tools=None,  # SIN HERRAMIENTAS
            generation_config=generation_config,
            system_instruction=system_instruction
        )

        print(f"✅ Gemini model initialized successfully WITHOUT tools")
        logger.info(f"Gemini model initialized with market data context")
        return model

    except Exception as e:
        logger.error(f"Error initializing Gemini model without tools: {e}")
        print(f"❌ Error initializing Gemini model: {e}")
        return None

async def generate_chat_response(
    messages: List[Dict[str, str]],
    model: Optional[GenerativeModel]
) -> Dict[str, Any]:
    """
    Generate a chat response using the Gemini model.
    """
    if model is None:
        logger.warning("Vertex AI model not available, returning fallback response")
        return {
            "type": "text",
            "content": "Lo siento, el servicio de IA no está disponible en este momento."
        }

    try:
        contents_history = []
        for message in messages:
            role = "model" if message.get("role") == "assistant" else "user"
            content = message.get("content", "").strip()
            if content:
                contents_history.append(Content(role=role, parts=[Part.from_text(content)]))

        if not contents_history:
            return {"type": "text", "content": "Por favor, envía un mensaje."}

        response = model.generate_content(contents_history)

        # Verificación segura de la respuesta
        if not response or not response.candidates:
            return {"type": "text", "content": "No se recibió una respuesta válida del modelo."}

        candidate = response.candidates[0]
        if not candidate.content or not candidate.content.parts:
            return {"type": "text", "content": "La respuesta del modelo está vacía."}

        # Acceso directo al texto (estrategia funcional)
        if len(candidate.content.parts) > 0:
            first_part = candidate.content.parts[0]

            # Prioridad 1: Texto directo
            if hasattr(first_part, 'text') and first_part.text and first_part.text.strip():
                return {"type": "text", "content": first_part.text.strip()}

            # Prioridad 2: Function call
            if hasattr(first_part, 'function_call') and first_part.function_call:
                function_call = first_part.function_call
                function_name = function_call.name
                function_args = {key: value for key, value in function_call.args.items()}
                logger.info(f"Function call detected: {function_name} with args {function_args}")
                return {
                    "type": "function_call",
                    "function_name": function_name,
                    "function_args": function_args
                }

            # Verificar thought_signature
            if 'thought_signature' in str(first_part):
                logger.warning("Model returned thought_signature, this should not happen without tools")
                return {"type": "text", "content": "El modelo devolvió una respuesta interna. Por favor, intenta reformular tu pregunta."}

        # Fallback final
        return {"type": "text", "content": "No se pudo procesar la respuesta del modelo."}

    except Exception as e:
        logger.error(f"Exception in generate_chat_response: {str(e)}")
        traceback_str = traceback.format_exc()
        logger.error(f"Traceback: {traceback_str}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate AI response: {str(e)}"
        )


async def process_function_call_result(
    function_name: str,
    function_result: Any,
    conversation_history: List[Dict[str, str]],  # Cambiado para usar lista estructurada
    model: GenerativeModel
) -> str:
    """
    Process the result of a function call and generate a final response.
    Usa un enfoque estructurado con Content para mejor contexto.
    """
    try:
        # Construir historial estructurado
        contents_history = []
        for message in conversation_history:
            role = "model" if message.get("role") == "assistant" else "user"
            content = message.get("content", "").strip()
            if content:
                contents_history.append(Content(role=role, parts=[Part.from_text(content)]))

        # Añadir el resultado de la función como respuesta del modelo
        function_response = f"""
Histoal de la función {function_name}:
{json.dumps(function_result, indent=2, ensure_ascii=False)}

Por favor, analiza estos datos y proporciona una respuesta clara y útil al usuario en español.
Recuerda incluir el descargo de responsabilidad sobre asesoramiento financiero."""

        contents_history.append(Content(role="model", parts=[Part.from_text(function_response)]))

        # Añadir solicitud para generar respuesta final
        contents_history.append(Content(role="user", parts=[Part.from_text(
            "Analiza los datos anteriores y proporciona una respuesta clara y útil en español."
        )]))

        print("🤖 === CALLING model.generate_content FOR FUNCTION RESULT WITH STRUCTURED CONTENT ====")

        try:
            response = model.generate_content(contents_history)

            # Aplicar la misma lógica de detección de thought_signature que en generate_chat_response
            if response and response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]

                if candidate.content and candidate.content.parts and len(candidate.content.parts) > 0:
                    first_part = candidate.content.parts[0]

                    # Acceso directo al texto (estrategia funcional)
                    if hasattr(first_part, 'text') and first_part.text and first_part.text.strip():
                        direct_text = first_part.text.strip()
                        print("✅ === ACCESO DIRECTO AL TEXTO EN FUNCTION RESULT ===")
                        return direct_text

                    # Verificar si hay thought_signature
                    if 'thought_signature' in str(first_part):
                        print("🧠 === THOUGHT_SIGNATURE DETECTADO EN FUNCTION RESULT - USANDO FALLBACK ===")
                        # Ir directo al fallback manual
                    else:
                        # Verificar partes individuales si no hay thought_signature
                        texts = []
                        for part in candidate.content.parts:
                            if hasattr(part, 'text') and part.text and part.text.strip():
                                texts.append(part.text.strip())

                        if texts:
                            combined_text = " ".join(texts)
                            print("✅ Returning combined text from parts")
                            return combined_text

            # Fallback manual cuando hay thought_signature o no hay contenido útil
            print("⚡ === GENERANDO RESPUESTA MANUAL PARA FUNCTION RESULT ===")

            # Generar respuesta mejorada basada en los datos de la función
            if function_name == "get_price_data" and isinstance(function_result, dict):
                symbol = function_result.get("symbol", "el activo")
                latest_price = function_result.get("latest_price")
                bars_count = function_result.get("bars_count", 0)
                data = function_result.get("data", [])

                if latest_price:
                    # Calcular cambio si hay datos históricos
                    change_info = ""
                    if len(data) >= 2:
                        try:
                            current_price = data[-1].get("close", latest_price)
                            previous_price = data[-2].get("close", current_price)
                            change = current_price - previous_price
                            change_pct = (change / previous_price) * 100 if previous_price != 0 else 0
                            change_symbol = "📈" if change >= 0 else "📉"
                            change_info = f"\n\n{change_symbol} Cambio: ${change:+.2f} ({change_pct:+.2f}%) respecto al período anterior."
                        except:
                            pass

                    return f"💰 **Precio de {symbol}**: ${latest_price:.2f}{change_info}\n\nDatos obtenidos de {bars_count} períodos para análisis técnico. Esta información es solo para fines educativos y no constituye asesoramiento financiero."
                else:
                    return f"📊 He obtenido datos históricos de {symbol} con {bars_count} períodos. Los datos están disponibles para análisis técnico, aunque no pude determinar el precio más reciente. Esta información es solo para fines educativos."

            elif function_name == "apply_indicator" and isinstance(function_result, dict):
                symbol = function_result.get("symbol", "el activo")
                indicator = function_result.get("indicator", "el indicador")
                latest_value = function_result.get("latest_value", {})

                if latest_value and "value" in latest_value:
                    value = latest_value["value"]

                    # Añadir interpretación específica según el indicador
                    interpretation = ""
                    if indicator.upper() == "RSI":
                        if value > 70:
                            interpretation = " (Zona de sobrecompra - posible señal de venta)"
                        elif value < 30:
                            interpretation = " (Zona de sobreventa - posible señal de compra)"
                        else:
                            interpretation = " (Zona neutral)"
                    elif indicator.upper() == "MACD":
                        interpretation = f" (Valor {'positivo' if value > 0 else 'negativo'} - tendencia {'alcista' if value > 0 else 'bajista'})"

                    return f"📈 **{indicator} de {symbol}**: {value:.2f}{interpretation}\n\nEste indicador técnico puede ayudar en el análisis de tendencias y momentum. Esta información es solo para fines educativos."
                else:
                    return f"📊 He calculado el {indicator} para {symbol}. Los datos del indicador están disponibles para análisis, aunque no pude obtener el valor más reciente. Esta información es solo para fines educativos."

            # Fallback genérico mejorado
            return f"✅ He procesado exitosamente la función {function_name}. Los datos están disponibles para análisis financiero. Esta información es solo para fines educativos y no constituye asesoramiento financiero."

        except Exception as e:
            print(f"⚠️ Error in function result processing: {e}")
            return f"He obtenido los datos solicitados, pero hubo un problema al generar la respuesta final. Esta información es solo para fines educativos."

    except Exception as e:
        logger.error(f"Error processing function call result: {str(e)}")
        print(f"❌ Error processing function call result: {str(e)}")
        return f"Error procesando el resultado de {function_name}. Por favor, intenta de nuevo."